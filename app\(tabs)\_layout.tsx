import { Tabs, Redirect, usePathname } from 'expo-router';
import { StyleSheet, ActivityIndicator, View } from 'react-native';
import {
  User,
  ChefHat,
  HeartPulse,
  Chrome as Home,
  Dumbbell,
  Grid2x2X as Grid2X2,
  Grid2x2 as Grid<PERSON><PERSON>,
  <PERSON>K<PERSON><PERSON>,
  Brain,
} from 'lucide-react-native';
import { theme } from '@/constants/theme';
import { useAuth } from '@clerk/clerk-expo';

// Define styles after theme import but before any usage
const styles = StyleSheet.create({
  tabBarLabel: {
    fontFamily: theme.typography.fontFamily.medium,
    fontSize: 11,
  },
});

export default function TabLayout() {
  const { isSignedIn, isLoaded: isAuthLoaded } = useAuth();

  if (isSignedIn) {
    return (
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: theme.colors.primary,
          tabBarInactiveTintColor: theme.colors.gray[400],
          tabBarLabelStyle: styles.tabBarLabel,
          headerShown: false,
          tabBarShowLabel: true,
          tabBarPosition: 'bottom',
          tabBarStyle: {
            padding: 15,
            backgroundColor: '#fcfcfc',
          },
          animation: 'none',
          popToTopOnBlur: true,
        }}
        backBehavior="order"
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Overview',
            tabBarIcon: ({ color, size }) => (
              <SquareKanban size={20} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="health"
          options={{
            title: 'Health',
            tabBarIcon: ({ color, size }) => (
              <HeartPulse size={20} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="wellbeing"
          options={{
            title: 'Wellbeing',
            tabBarIcon: ({ color, size }) => <Brain size={20} color={color} />,
          }}
        />
        <Tabs.Screen
          name="fitness"
          options={{
            title: 'Fitness',
            tabBarIcon: ({ color, size }) => (
              <Dumbbell size={20} color={color} />
            ),
          }}
        />
        <Tabs.Screen
          name="profile"
          options={{
            title: 'Profile',
            tabBarIcon: ({ color, size }) => <User size={20} color={color} />,
          }}
        />
      </Tabs>
    );
  }
}
